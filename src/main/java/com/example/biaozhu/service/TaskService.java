package com.example.biaozhu.service;

import com.example.biaozhu.entity.Task;
import com.example.biaozhu.entity.User;
import com.example.biaozhu.payload.request.TaskAssignmentRequest;
import com.example.biaozhu.payload.request.TaskRequest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

/**
 * 任务服务接口，提供任务相关的业务逻辑功能
 */
public interface TaskService {
    
    /**
     * 获取所有任务
     * 
     * @return 任务列表
     */
    List<Task> getAllTasks();
    
    /**
     * 分页获取任务
     * 
     * @param pageable 分页参数
     * @return 任务分页结果
     */
    Page<Task> getTasks(Pageable pageable);
    
    /**
     * 根据项目ID获取任务
     * 
     * @param projectId 项目ID
     * @return 任务列表
     */
    List<Task> getTasksByProject(Long projectId);
    
    /**
     * 根据数据集ID获取任务
     * 
     * @param datasetId 数据集ID
     * @return 任务列表
     */
    List<Task> getTasksByDataset(Long datasetId);
    
    /**
     * 根据ID获取任务
     * 
     * @param id 任务ID
     * @return 任务对象
     */
    Task getTaskById(Long id);
    
    /**
     * 创建新任务
     * 
     * @param task 任务对象
     * @return 保存后的任务对象
     */
    Task createTask(Task task);
    
    /**
     * 创建新任务
     * 
     * @param taskRequest 任务请求对象
     * @param creator 创建者
     * @return 保存后的任务对象
     */
    Task createTask(TaskRequest taskRequest, User creator);
    
    /**
     * 更新任务信息
     * 
     * @param id 任务ID
     * @param taskDetails 任务详细信息
     * @return 更新后的任务对象
     */
    Task updateTask(Long id, Task taskDetails);
    
    /**
     * 更新任务信息
     * 
     * @param id 任务ID
     * @param taskRequest 任务请求信息
     * @return 更新后的任务对象
     */
    Task updateTask(Long id, TaskRequest taskRequest);
    
    /**
     * 删除任务
     * 
     * @param id 任务ID
     */
    void deleteTask(Long id);
    
    /**
     * 分配任务给用户
     * 
     * @param taskId 任务ID
     * @param userId 用户ID
     * @return 更新后的任务对象
     */
    Task assignTask(Long taskId, Long userId);
    
    /**
     * 分配任务给用户
     * 
     * @param taskId 任务ID
     * @param request 任务分配请求
     * @return 更新后的任务对象
     */
    Task assignTask(Long taskId, TaskAssignmentRequest request);
    
    /**
     * 提交任务完成
     * 
     * @param taskId 任务ID
     * @return 更新后的任务对象
     */
    Task submitTask(Long taskId);
    
    /**
     * 批准任务
     * 
     * @param taskId 任务ID
     * @param reviewComments 审核意见
     * @return 更新后的任务对象
     */
    Task approveTask(Long taskId, String reviewComments);
    
    /**
     * 驳回任务
     * 
     * @param taskId 任务ID
     * @param reviewComments 审核意见
     * @return 更新后的任务对象
     */
    Task rejectTask(Long taskId, String reviewComments);
    
    /**
     * 获取分配给用户的任务
     * 
     * @param userId 用户ID
     * @return 任务列表
     */
    List<Task> getTasksAssignedToUser(Long userId);
    
    /**
     * 获取用户需要审核的任务
     * 
     * @param userId 用户ID
     * @return 任务列表
     */
    List<Task> getTasksToReviewByUser(Long userId);
    
    /**
     * 获取任务统计信息
     * 
     * @param taskId 任务ID
     * @return 统计信息映射
     */
    Map<String, Object> getTaskStatistics(Long taskId);
    
    /**
     * 获取项目任务统计信息
     * 
     * @param projectId 项目ID
     * @return 统计信息映射
     */
    Map<String, Object> getProjectTasksStatistics(Long projectId);
    
    /**
     * 分页获取所有任务
     * 
     * @param page 页码
     * @param size 每页大小
     * @param sort 排序字段
     * @return 分页任务结果
     */
    Page<Task> getAllTasks(int page, int size, String sort);
    
    /**
     * 根据项目ID和状态获取任务
     * 
     * @param projectId 项目ID
     * @param status 任务状态
     * @param page 页码
     * @param size 每页大小
     * @param sort 排序字段
     * @return 分页任务结果
     */
    Page<Task> getTasksByProject(Long projectId, String status, int page, int size, String sort);
    
    /**
     * 根据用户和状态获取任务
     * 
     * @param user 用户
     * @param status 任务状态
     * @param page 页码
     * @param size 每页大小
     * @param sort 排序字段
     * @return 分页任务结果
     */
    Page<Task> getTasksByUser(User user, String status, int page, int size, String sort);
    
    /**
     * 开始任务
     * 
     * @param taskId 任务ID
     * @return 更新后的任务
     */
    Task startTask(Long taskId);
    
    /**
     * 完成任务
     * 
     * @param taskId 任务ID
     * @return 更新后的任务
     */
    Task completeTask(Long taskId);
    
    /**
     * 审核任务
     * 
     * @param taskId 任务ID
     * @param reviewer 审核人
     * @param approved 是否批准
     * @param comments 审核意见
     * @return 更新后的任务
     */
    Task reviewTask(Long taskId, User reviewer, boolean approved, String comments);
    
    /**
     * 获取任务状态统计
     * 
     * @return 状态统计信息
     */
    Map<String, Long> getTaskStatusStatistics();
    
    /**
     * 获取标注员绩效
     * 
     * @param days 天数
     * @return 绩效统计信息
     */
    List<Map<String, Object>> getAnnotatorPerformance(int days);
    
    /**
     * 导出任务数据
     *
     * @param taskId 任务ID
     * @param format 导出格式
     * @return 导出的数据
     */
    byte[] exportTaskData(Long taskId, String format);

    /**
     * 计算任务的已完成标注数量
     *
     * @param taskId 任务ID
     * @return 已完成的标注数量
     */
    int getCompletedAnnotationsCount(Long taskId);

    /**
     * 检查当前用户是否是任务的分配者
     *
     * @param taskId 任务ID
     * @return 是否是任务分配者
     */
    boolean isTaskAssignee(Long taskId);

    /**
     * 检查当前用户是否是任务的创建者
     *
     * @param taskId 任务ID
     * @return 是否是任务创建者
     */
    boolean isTaskCreator(Long taskId);

    /**
     * 检查当前用户是否是任务的成员（分配者或创建者）
     *
     * @param taskId 任务ID
     * @return 是否是任务成员
     */
    boolean isTaskMember(Long taskId);
}