package com.example.biaozhu.controller;

import com.example.biaozhu.entity.Task;
import com.example.biaozhu.entity.User;
import com.example.biaozhu.entity.Annotation;
import com.example.biaozhu.payload.request.TaskAssignmentRequest;
import com.example.biaozhu.payload.request.TaskRequest;
import com.example.biaozhu.payload.request.AnnotationRequest;
import com.example.biaozhu.payload.response.MessageResponse;
import com.example.biaozhu.payload.response.TaskResponse;
import com.example.biaozhu.payload.response.AnnotationResponse;
import com.example.biaozhu.service.TaskService;
import com.example.biaozhu.service.UserService;
import com.example.biaozhu.service.AnnotationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 任务控制器
 * 处理标注任务管理相关请求
 */
@RestController
@RequestMapping("/tasks")
public class TaskController {

    private final TaskService taskService;
    private final UserService userService;
    private final AnnotationService annotationService;

    @Autowired
    public TaskController(TaskService taskService, UserService userService, AnnotationService annotationService) {
        this.taskService = taskService;
        this.userService = userService;
        this.annotationService = annotationService;
    }
    
    /**
     * 创建新标注任务
     * 
     * @param taskRequest 任务请求对象
     * @return 创建的任务信息
     */
    @PostMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<?> createTask(@Valid @RequestBody TaskRequest taskRequest) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        User currentUser = userService.getUserByUsername(authentication.getName());

        Task task = taskService.createTask(taskRequest, currentUser);
        int completedAnnotations = taskService.getCompletedAnnotationsCount(task.getId());
        return new ResponseEntity<>(new TaskResponse(task, completedAnnotations), HttpStatus.CREATED);
    }
    
    /**
     * 获取所有标注任务（分页）
     * 管理员和经理可以查看所有任务，标注员只能查看分配给自己的任务
     *
     * @param page 页码
     * @param size 每页大小
     * @param sort 排序字段
     * @return 任务分页数据
     */
    @GetMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER') or hasRole('ANNOTATOR') or hasRole('DATA_LABELER')")
    public ResponseEntity<?> getAllTasks(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sort) {

        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        User currentUser = userService.getUserByUsername(authentication.getName());

        // 检查用户角色
        boolean isAdmin = authentication.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals("ROLE_ADMIN"));
        boolean isManager = authentication.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals("ROLE_MANAGER"));
        boolean isAnnotator = authentication.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals("ROLE_ANNOTATOR") ||
                              a.getAuthority().equals("ROLE_DATA_LABELER"));

        Page<Task> tasksPage;

        // 根据角色获取不同的任务数据
        if (isAdmin || isManager) {
            // 管理员和经理可以查看所有任务
            tasksPage = taskService.getAllTasks(page, size, sort);
        } else if (isAnnotator) {
            // 标注员只能查看分配给自己的任务
            tasksPage = taskService.getTasksByUser(currentUser, null, page, size, sort);
        } else {
            // 其他角色无权限访问
            tasksPage = taskService.getAllTasks(page, size, sort); // 默认行为，但实际上不应该到达这里
        }

        Map<String, Object> response = new HashMap<>();
        response.put("tasks", tasksPage.getContent().stream()
                .map(task -> {
                    int completedAnnotations = taskService.getCompletedAnnotationsCount(task.getId());
                    return new TaskResponse(task, completedAnnotations);
                })
                .collect(Collectors.toList()));
        response.put("currentPage", tasksPage.getNumber());
        response.put("totalItems", tasksPage.getTotalElements());
        response.put("totalPages", tasksPage.getTotalPages());

        return new ResponseEntity<>(response, HttpStatus.OK);
    }
    
    /**
     * 获取当前用户的标注任务
     * 
     * @param status 任务状态（可选）
     * @param page 页码
     * @param size 每页大小
     * @param sort 排序字段
     * @return 任务分页数据
     */
    @GetMapping("/me")
    public ResponseEntity<?> getMyTasks(
            @RequestParam(required = false) String status,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sort) {
        
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        User currentUser = userService.getUserByUsername(authentication.getName());
        
        Page<Task> tasksPage = taskService.getTasksByUser(currentUser, status, page, size, sort);

        Map<String, Object> response = new HashMap<>();
        response.put("tasks", tasksPage.getContent().stream()
                .map(task -> {
                    int completedAnnotations = taskService.getCompletedAnnotationsCount(task.getId());
                    return new TaskResponse(task, completedAnnotations);
                })
                .collect(Collectors.toList()));
        response.put("currentPage", tasksPage.getNumber());
        response.put("totalItems", tasksPage.getTotalElements());
        response.put("totalPages", tasksPage.getTotalPages());
        
        return new ResponseEntity<>(response, HttpStatus.OK);
    }
    
    /**
     * 获取项目下的所有任务
     * 
     * @param projectId 项目ID
     * @param status 任务状态（可选）
     * @param page 页码
     * @param size 每页大小
     * @param sort 排序字段
     * @return 任务分页数据
     */
    @GetMapping("/project/{projectId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER') or @projectService.isProjectMember(#projectId)")
    public ResponseEntity<?> getTasksByProject(
            @PathVariable Long projectId,
            @RequestParam(required = false) String status,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sort) {
        
        Page<Task> tasksPage = taskService.getTasksByProject(projectId, status, page, size, sort);

        Map<String, Object> response = new HashMap<>();
        response.put("tasks", tasksPage.getContent().stream()
                .map(task -> {
                    int completedAnnotations = taskService.getCompletedAnnotationsCount(task.getId());
                    return new TaskResponse(task, completedAnnotations);
                })
                .collect(Collectors.toList()));
        response.put("currentPage", tasksPage.getNumber());
        response.put("totalItems", tasksPage.getTotalElements());
        response.put("totalPages", tasksPage.getTotalPages());
        
        return new ResponseEntity<>(response, HttpStatus.OK);
    }
    
    /**
     * 根据ID获取任务
     *
     * @param id 任务ID
     * @return 任务信息响应
     */
    @GetMapping("/{id}")
    public ResponseEntity<?> getTaskById(@PathVariable Long id) {
        Task task = taskService.getTaskById(id);
        int completedAnnotations = taskService.getCompletedAnnotationsCount(id);
        return ResponseEntity.ok(new TaskResponse(task, completedAnnotations));
    }
    
    /**
     * 更新任务信息
     * 
     * @param id 任务ID
     * @param taskRequest 任务请求对象
     * @return 更新后的任务信息
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER') or @taskService.isTaskCreator(#id)")
    public ResponseEntity<?> updateTask(
            @PathVariable Long id,
            @Valid @RequestBody TaskRequest taskRequest) {

        Task updatedTask = taskService.updateTask(id, taskRequest);
        int completedAnnotations = taskService.getCompletedAnnotationsCount(id);
        return ResponseEntity.ok(new TaskResponse(updatedTask, completedAnnotations));
    }
    
    /**
     * 删除任务
     * 
     * @param id 任务ID
     * @return 删除成功响应
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER') or @taskService.isTaskCreator(#id)")
    public ResponseEntity<?> deleteTask(@PathVariable Long id) {
        taskService.deleteTask(id);
        return ResponseEntity.ok(new MessageResponse("任务删除成功"));
    }
    
    /**
     * 分配任务给标注者
     * 
     * @param id 任务ID
     * @param assignmentRequest 任务分配请求
     * @return 分配结果消息
     */
    @PostMapping("/{id}/assign")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<?> assignTask(
            @PathVariable Long id,
            @Valid @RequestBody TaskAssignmentRequest assignmentRequest) {
        
        taskService.assignTask(id, assignmentRequest);
        return ResponseEntity.ok(new MessageResponse("任务分配成功"));
    }
    
    /**
     * 开始任务
     * 
     * @param id 任务ID
     * @return 操作结果消息
     */
    @PutMapping("/{id}/start")
    @PreAuthorize("@taskService.isTaskAssignee(#id)")
    public ResponseEntity<?> startTask(@PathVariable Long id) {
        taskService.startTask(id);
        return ResponseEntity.ok(new MessageResponse("任务已开始"));
    }


    
    /**
     * 完成任务
     * 
     * @param id 任务ID
     * @return 操作结果消息
     */
    @PutMapping("/{id}/complete")
    @PreAuthorize("@taskService.isTaskAssignee(#id)")
    public ResponseEntity<?> completeTask(@PathVariable Long id) {
        taskService.completeTask(id);
        return ResponseEntity.ok(new MessageResponse("任务已完成"));
    }
    
    /**
     * 审核任务
     * 
     * @param id 任务ID
     * @param approved 是否批准
     * @param comments 审核意见
     * @return 审核结果消息
     */
    @PutMapping("/{id}/review")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER') or hasRole('REVIEWER')")
    public ResponseEntity<?> reviewTask(
            @PathVariable Long id,
            @RequestParam boolean approved,
            @RequestParam(required = false) String comments) {
        
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        User reviewer = userService.getUserByUsername(authentication.getName());
        
        taskService.reviewTask(id, reviewer, approved, comments);
        return ResponseEntity.ok(new MessageResponse("任务已" + (approved ? "批准" : "拒绝")));
    }
    
    /**
     * 获取任务统计信息
     * 
     * @param id 任务ID
     * @return 任务统计信息
     */
    @GetMapping("/{id}/statistics")
    public ResponseEntity<?> getTaskStatistics(@PathVariable Long id) {
        Map<String, Object> statistics = taskService.getTaskStatistics(id);
        return ResponseEntity.ok(statistics);
    }
    
    /**
     * 按状态获取任务数量统计
     * 
     * @return 任务状态统计信息
     */
    @GetMapping("/status-statistics")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<?> getTaskStatusStatistics() {
        Map<String, Long> statistics = taskService.getTaskStatusStatistics();
        return ResponseEntity.ok(statistics);
    }
    
    /**
     * 获取标注者性能统计
     * 
     * @param days 统计天数
     * @return 标注者性能统计信息
     */
    @GetMapping("/annotator-performance")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<?> getAnnotatorPerformance(
            @RequestParam(defaultValue = "30") int days) {
        
        List<Map<String, Object>> statistics = taskService.getAnnotatorPerformance(days);
        return ResponseEntity.ok(statistics);
    }
    
    /**
     * 导出任务数据
     * 
     * @param id 任务ID
     * @param format 导出格式
     * @return 导出的数据
     */
    @GetMapping("/{id}/export")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER') or hasRole('REVIEWER') or @taskService.isTaskAssignee(#id)")
    public ResponseEntity<?> exportTaskData(
            @PathVariable Long id,
            @RequestParam(defaultValue = "json") String format) {
        
        byte[] data = taskService.exportTaskData(id, format);
        
        // 确定内容类型
        String contentType;
        String filename;
        
        if ("json".equalsIgnoreCase(format)) {
            contentType = "application/json";
            filename = "task_" + id + ".json";
        } else if ("csv".equalsIgnoreCase(format)) {
            contentType = "text/csv";
            filename = "task_" + id + ".csv";
        } else if ("excel".equalsIgnoreCase(format) || "xlsx".equalsIgnoreCase(format)) {
            contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            filename = "task_" + id + ".xlsx";
        } else {
            contentType = "application/octet-stream";
            filename = "task_" + id + "." + format;
        }
        
        return ResponseEntity.ok()
                .contentType(MediaType.parseMediaType(contentType))
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                .body(data);
    }

    /**
     * 新增标注
     *
     * @param taskId 任务ID
     * @param annotationRequest 标注请求对象
     * @return 创建的标注信息
     */
    @PostMapping("/{taskId}/annotations")
    @PreAuthorize("@taskService.isTaskAssignee(#taskId) or hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<?> createAnnotation(
            @PathVariable Long taskId,
            @Valid @RequestBody AnnotationRequest annotationRequest) {

        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        User currentUser = userService.getUserByUsername(authentication.getName());

        // 验证任务是否存在
        Task task = taskService.getTaskById(taskId);

        // 设置任务ID到请求对象中
        annotationRequest.setTaskId(taskId);

        // 创建标注
        Annotation annotation = annotationService.createAnnotation(annotationRequest, currentUser);

        return new ResponseEntity<>(new AnnotationResponse(annotation), HttpStatus.CREATED);
    }
}